/* Balkan Cloud Experts - Clean Professional Styles */
/* Modern, clean design for B2B cloud services */

[x-cloak] { 
    display: none !important;
}

/* Base styles */
body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

/* CSS Variables */
:root {
    /* Primary Brand Colors */
    --primary-color: #023679;        /* Main brand color */
    --primary-light: #0d4a8c;        /* Lighter shade */
    --primary-dark: #012654;         /* Darker shade */
    
    /* Accent Colors */
    --accent-blue: #0ea5e9;          /* Sky 500 - Cloud/Tech */
    --accent-cyan: #06b6d4;          /* Cyan 500 - Digital */
    --accent-green: #10b981;         /* Emerald 500 - Success */
    
    /* Text Colors */
    --text-dark: #1f2937;            /* Gray 800 */
    --text-medium: #4b5563;          /* Gray 600 */
    --text-light: #6b7280;           /* Gray 500 */
    --text-muted: #9ca3af;           /* Gray 400 */
    
    /* Background Colors */
    --bg-primary: #ffffff;           /* White */
    --bg-secondary: #f8fafc;         /* Slate 50 */
    --bg-tertiary: #f1f5f9;          /* Slate 100 */
    --bg-dark: #023679;              /* Main brand color for dark backgrounds */
    
    /* Border Colors */
    --border-light: #e2e8f0;         /* Slate 200 */
    --border-medium: #cbd5e1;        /* Slate 300 */
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    color: var(--text-dark);
    font-weight: 600;
    line-height: 1.2;
}

/* Custom brand colors */
.text-brand-primary {
    color: #023679;
}

.text-primary {
    color: var(--primary-color);
}

.text-accent-blue {
    color: var(--accent-blue);
}

.text-accent-cyan {
    color: var(--accent-cyan);
}

.bg-primary {
    background-color: var(--primary-color);
}

.bg-accent-blue {
    background-color: var(--accent-blue);
}

.bg-accent-cyan {
    background-color: var(--accent-cyan);
}

.bg-accent-green {
    background-color: var(--accent-green);
}

/* Navigation */
.nav-link {
    color: #6b7280;
    font-weight: 500;
    transition: color 0.2s ease;
    text-decoration: none;
}

.nav-link:hover {
    color: var(--primary-color);
}

/* Glassmorphism Header */
header {
    background: rgba(255, 255, 255, 0.85) !important;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Buttons */
.btn-primary {
    background: linear-gradient(135deg, #023679 0%, #0d4a8c 100%);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    border: none;
    cursor: pointer;
    box-shadow: 0 2px 4px -1px rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.06);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #012654 0%, #023679 100%);
    transform: translateY(-1px);
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

.btn-secondary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background-color: transparent;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    cursor: pointer;
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

.btn-accent {
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-cyan) 100%);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    border: none;
    cursor: pointer;
}

.btn-accent:hover {
    transform: translateY(-1px);
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* Hero Secondary Button - White with primary color on hover */
.btn-hero-secondary {
    background-color: white;
    color: var(--primary-color);
    border: 2px solid white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    cursor: pointer;
    box-shadow: 0 2px 4px -1px rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.06);
}

.btn-hero-secondary:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* Cards with Glassmorphism */
.card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 1rem;
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    border: 1px solid rgba(255, 255, 255, 0.18);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.25);
    background: rgba(255, 255, 255, 0.98);
}

/* Service Cards */
.service-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 1.25rem;
    padding: 2.5rem;
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    border: 1px solid rgba(255, 255, 255, 0.18);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #023679 0%, #0d4a8c 100%);
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border-color: rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.98);
}

/* Tech Icons */
.tech-icon {
    width: 4rem;
    height: 4rem;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.5rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    transition: all 0.3s ease;
}

.tech-icon:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* Footer */
footer,
footer.bg-primary,
footer.bg-gray-900,
footer.text-white {
    background-color: #374151 !important;
    color: #e5e7eb !important;
}

footer h4,
footer .font-bold {
    color: #ffffff !important;
    font-weight: 600;
}

footer a,
footer .text-gray-300 {
    color: #d1d5db !important;
    transition: color 0.2s ease;
}

footer a:hover,
footer .hover\\:text-white:hover {
    color: #ffffff !important;
}

footer p,
footer span,
footer div {
    color: #e5e7eb !important;
}

/* Hero Section */
.hero-gradient,
section.hero-gradient {
    background: linear-gradient(135deg, rgba(2, 54, 121, 0.85) 0%, rgba(1, 38, 84, 0.85) 50%, rgba(13, 74, 140, 0.85) 100%) !important;
    background-size: cover !important;
    background-position: center !important;
    background-image: linear-gradient(135deg, rgba(2, 54, 121, 0.85) 0%, rgba(1, 38, 84, 0.85) 50%, rgba(13, 74, 140, 0.85) 100%) !important;
}

.hero-gradient h1,
.hero-gradient h2,
.hero-gradient h3,
.hero-gradient h4,
.hero-gradient h5,
.hero-gradient h6 {
    color: white !important;
}

/* Hero with background image for index page */
.hero-with-image {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    padding-top: 6rem;
    padding-bottom: 4rem;
}

.hero-with-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('./images/hero.png'), url('../images/hero.png'), url('/images/hero.png');
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    z-index: -2;
}

.hero-with-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(2, 54, 121, 0.75) 0%, rgba(1, 38, 84, 0.75) 50%, rgba(13, 74, 140, 0.75) 100%);
    z-index: -1;
}

.hero-pattern {
    background-image:
        radial-gradient(circle at 25% 25%, rgba(14, 165, 233, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(6, 182, 212, 0.1) 0%, transparent 50%);
}

/* CTA Sections */
.cta-section {
    background-color: #ffffff;
}

.cta-section h2 {
    color: var(--primary-color) !important;
}

.cta-section p {
    color: #4b5563 !important; /* Same as text-gray-600 */
}

/* Form Styles */
.form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: border-color 0.2s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(2, 54, 121, 0.1);
}

.form-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 1rem;
    min-height: 120px;
    resize: vertical;
    transition: border-color 0.2s ease;
}

.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(2, 54, 121, 0.1);
}

/* Stats/Numbers */
.stat-number {
    font-size: 3rem;
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-blue) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: 0.5rem;
    display: block;
}

/* Feature Lists */
.feature-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.feature-icon {
    width: 1.25rem;
    height: 1.25rem;
    background-color: var(--accent-green);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.75rem;
    font-weight: bold;
    flex-shrink: 0;
}

/* Milestone/Step Indicators */
.milestone-indicator {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    font-weight: bold;
    margin-top: 0.25rem;
    box-shadow: 0 2px 4px -1px rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.06);
    flex-shrink: 0;
}

.milestone-indicator.step-1 {
    background: var(--accent-blue);
}

.milestone-indicator.step-2 {
    background: var(--accent-cyan);
}

.milestone-indicator.step-3 {
    background: var(--accent-green);
}

.milestone-indicator.step-4 {
    background: var(--primary-color);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
}

.animate-on-scroll.fade-in-up {
    opacity: 1;
    transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-gradient,
    .hero-with-image {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }

    .service-card {
        padding: 1.5rem;
    }

    .tech-icon {
        width: 3rem;
        height: 3rem;
        font-size: 1.25rem;
    }

    .stat-number {
        font-size: 2.5rem;
    }
}

@media (max-width: 640px) {
    .hero-gradient,
    .hero-with-image {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }
}

/* Print Styles */
@media print {
    .fixed,
    .btn-primary,
    .btn-secondary,
    .btn-accent,
    footer {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .hero-gradient,
    .hero-with-image {
        background: white !important;
        color: black !important;
    }
}
