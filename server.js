const express = require('express');
const nodemailer = require('nodemailer');
const path = require('path');
require('dotenv').config();

const app = express();
app.use(express.json());
app.use(express.static(path.join(__dirname)));

const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS
  }
});

app.post('/api/sendEmail', async (req, res) => {
  const { name, email, company, phone, service, message } = req.body;

  const mailOptions = {
    from: process.env.EMAIL_USER,
    to: process.env.EMAIL_TO,
    subject: 'Neue Anfrage von Balkan Cloud Experts Website',
    html: `
      <h2>Neue Kundenanfrage</h2>
      <p><strong>Name:</strong> ${name}</p>
      <p><strong>E-Mail:</strong> ${email}</p>
      <p><strong>Unternehmen:</strong> ${company || 'Nicht angegeben'}</p>
      <p><strong>Telefon:</strong> ${phone || 'Nicht angegeben'}</p>
      <p><strong>Interessierte Dienstleistung:</strong> ${service || 'Nicht angegeben'}</p>
      <p><strong>Nachricht:</strong></p>
      <p>${message}</p>
      <hr>
      <p><small>Gesendet über balkancloudexperts.com Kontaktformular</small></p>
    `
  };

  try {
    await transporter.sendMail(mailOptions);
    res.status(200).json({ message: 'E-Mail erfolgreich gesendet' });
  } catch (error) {
    console.error('Fehler beim Senden der E-Mail:', error);
    res.status(500).json({ message: 'Fehler beim Senden der E-Mail' });
  }
});

const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`Balkan Cloud Experts Server läuft auf Port ${PORT}`);
});
